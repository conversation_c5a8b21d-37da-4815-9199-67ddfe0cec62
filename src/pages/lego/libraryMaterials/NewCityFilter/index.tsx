import { BLMOrgBtCity } from '@blmcp/peento-businessComponents';
import { useRef, useEffect, useMemo, useState } from 'react';
import { message } from '@blmcp/ui';
import { getCurrnetCityId } from '@/pages/lego/utils';
import { useDispatch } from '@/utils/store';
import { isHubble } from '@/utils/hubble';
import FilterWrapper from '../wrapper/FilterWrapper';
import DispatchWrapper from '../wrapper/DDispatchWrapper';
import stateContext, { StateProps } from '../../context/filterContext';
import linkageCenterExp from '../module/Linkage';

interface CityFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  // 数据源
  kind: 'link' | 'authOpen';
  uuid: string;
  useCodeList?: string[];
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

export const NewCityFilter = function (props: CityFilterProps) {
  const ref = useRef(null);

  const context = stateContext(props.uuid);
  const [state, dispatch] = useDispatch<StateProps>(context);
  const minAuthLevel =
    window.legoMinAuthLevel || window.parent.legoMinAuthLevel || 4;
  const [kind, setKind] = useState(minAuthLevel > 2 ? 'link' : 'authOpen');
  const linkageCenter = linkageCenterExp(props.uuid);
  useEffect(() => {
    const cityId = getCurrnetCityId();
    // 路径存在 城市，则设置上
    if (cityId) {
      dispatch({
        type: 'city',
        payload: [cityId],
      });
    }
  }, []);

  useEffect(() => {
    return linkageCenter.subscribe('setCityFilterKind', (val: string) => {
      setKind(val);
      // 每次更新清空选择的
      if (val !== kind) {
        dispatch({
          type: 'clear',
        });
      }
    });
  }, [kind]);

  return (
    <FilterWrapper
      storeField="city"
      componentProps={props}
      fieldProps={useMemo(() => {
        return {
          key: 'adcode',
          columnId: 100000011,
          dataType: 0,
        };
      }, [])}
      handleFieldLabel={(val: []) => {
        try {
          return val.length ? ref.current?.getSelectedLabelName?.() : [];
          // return (val.length ? ref.current?.getSelectedOptions?.() : [])
          //   .filter((f) => !f.cityHalfAuthFlag)
          //   .map((m) => m.name);
        } catch (error) {
          return [];
        }
      }}
    >
      <BLMOrgBtCity
        ref={ref}
        useCodeList={props.useCodeList}
        multiple
        key={kind}
        kind={kind}
        tenantId={state.tenant}
        structureType={2}
        getOptions={(options: any[]) => {
          const newOptions: any[] = [];
          options.forEach((item) => {
            newOptions.push({
              dictDesc: item.code,
              dictValue: item.name,
              childDictList: item.childList.map((m) => {
                return {
                  dictDesc: m.name,
                  dictValue: m.code,
                };
              }),
            });
          });
          // 智能归因会用到城市数据
          linkageCenter.notify('cityOptionsChange', newOptions);

          // 如果没有授权城市
          if (!options?.length) {
            message.error(
              isHubble
                ? '该租户暂未开城'
                : '当前账号未配置城市权限，请联系管理员',
            );
            // return;
          }
        }}
        openUaFlag
        showAll
      />
    </FilterWrapper>
  );
};
