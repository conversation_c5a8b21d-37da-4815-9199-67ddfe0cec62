/**
 * 日期组件
 */
import { useEffect, useState, useRef } from 'react';
import dayjs from 'dayjs';
// import { DatePickerFilter as DatePickerFilterView } from '../../components/Filter/DatePickerFilter';
import { DateRangePicker } from '@blmcp/web-ui';
import { getOtherPresets } from '../../components/Filter/DatePickerFilter/tools';
import FilterWrapper from '../wrapper/FilterWrapper';
import isMobile from '../../utils/isMobile';
import { DEFAULT_DATE_RANGE } from './constants/dateRangeOptions';

interface DatePickerFilterProps {
  title?: string;
  dataSetConfig: any;
  __id: any;
  componentId: any;
  defaultValue: any;
  dateRange?: number;
  uuid: string;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          onlyOne: true,
          ignoreSetDefaultComputeType: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
      dateOnly: true,
      indexDisabled: true,
    },
  },
  // 默认时间
  defaultValue: {
    componentName: 'DatePickerDefaultValue',
    // 该组件其他配置项
    props: {
      // 拖拽进入的，还需要配置数据集
      type: 'drop-component',
    },
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
  // 可选范围
  dateRange: {
    componentName: 'DatePickerRange',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 1,
};

const temp_dateMap: any = {};
setTimeout(() => {
  const { project } = window.AliLowCodeEngine || {};
  // 暂时通过监听历史变化，重置缓存，后面统一处理默认值问题
  project?.currentDocument?.history.onChangeCursor(() => {
    // eslint-disable-next-line guard-for-in
    for (let key in temp_dateMap) {
      delete temp_dateMap[key];
    }
  });
});

// 逻辑层
export const DatePickerFilter = ({
  dataSetConfig,
  __id,
  componentId,
  defaultValue,
  dateRange = DEFAULT_DATE_RANGE,
  uuid,
}: DatePickerFilterProps) => {
  // 重置强制刷新用
  const [key, setKey] = useState(() => Date.now());
  const defaultValueRef = useRef(defaultValue);
  const filterId = __id ?? componentId ?? '';
  const { dimensionInfo } = dataSetConfig ?? {};
  const dimension = dimensionInfo?.[0];
  const presets = getOtherPresets();
  const presetList = getOtherPresets();
  const [curDefaultValue, setCurDefaultValue] = React.useState(() => {
    // 如果 defaultValue 直接包含 value，优先使用
    if (defaultValue?.type === 'customDate') {
      return defaultValue?.value;
    }
    // 否则从预设列表中查找
    return (
      presetList.find((item: any) => item.type === defaultValue?.type)?.value ||
      presetList[0].value
    );
  });
  const curTimeType = useRef<any>(
    presetList.find((item: any) => item.type === defaultValue?.type)
      ?.timeType || presetList[0].timeType,
  );
  let selectNum = 0,
    changeNum = 0;
  const onSelect = (
    dateFilterRelativeUnit?: number,
    type?: string,
    handleChange?: any,
  ) => {
    selectNum += 1;
    // 预设范围选中回调，会在onChange之前调用
    curTimeType.current = dateFilterRelativeUnit;
    if (type === 'clear') {
      setCurDefaultValue([]);
      handleChange([], curTimeType.current);
      setKey(Date.now());
    }
  };
  const presetsFun = getOtherPresets(onSelect);
  // const presetsFun = getOtherPresets();
  // 根据默认值类型获取对应的日期项
  const getDefaultDateItem = () => {
    const dateType = defaultValue?.dateType;
    // 外部自定义默认时间
    if (dateType === 'customDate') {
      // 如果 defaultValue 中有自定义的 value，使用它；否则使用默认值
      const customValue =
        defaultValue?.value || presets.find((item) => item.type === 'last-7');
      return {
        label: null,
        title: '自定义',
        timeType: 6,
        type: 'customDate',
        value: customValue,
      };
    }
    // 清空场景
    if (dateType === 'all') {
      return presets.find((item) => item.type === 'clear-time');
    }

    // 快捷默认时间
    return (
      presets.find((item) => item.type === dateType) ||
      presets.find((item) => item.type === 'last-7')
    );
  };

  const defaultDateItem = getDefaultDateItem();

  useEffect(() => {
    if (defaultValue?.dateType === 'customDate') {
      defaultValueRef.current = defaultValue;
      temp_dateMap[filterId] = {
        value:
          defaultValue?.value || presets.find((item) => item.type === 'last-7'),
        type: 6, // CUSTOM 类型
      };
      setKey(Date.now());
    } else if (
      JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue)
    ) {
      defaultValueRef.current = defaultValue;
      temp_dateMap[filterId] = null;
      setKey(Date.now());
    }
  }, [defaultValue]);
  console.log(defaultValue, 'defaultValuefieldItem----4444', defaultDateItem);

  // 监听时间跨度变化，手动重置为当前默认值
  useEffect(() => {
    temp_dateMap[filterId] = {
      value: defaultDateItem?.value,
      type: temp_dateMap[filterId]?.type,
    };
    setKey(Date.now());
  }, [dateRange]);
  //支持动态渲染
  if (dimension) {
    return (
      <FilterWrapper
        componentProps={{
          dataSetConfig,
          __id,
          componentId,
          defaultValue,
          dateRange,
          uuid,
          // value: defaultDateItem?.value,
        }}
        defaultValue={defaultDateItem?.value}
        filterExtendField={(field) => {
          console.log(field, 'fieldItem----3333');
          return {
            fieldLabel: field.fieldValue?.length
              ? [
                  dayjs(field.fieldValue[0])
                    .startOf('day')
                    .format('YYYY-MM-DD') +
                    ' ~ ' +
                    dayjs(field.fieldValue[1])
                      .endOf('day')
                      .format('YYYY-MM-DD'),
                ]
              : [],
            // dateFilterRelativeUnit: this.dateFilterRelativeUnit,
          };
        }}
      >
        {({ value, onChange, fieldItem }) => {
          console.log(value, onChange, fieldItem, 'fieldItem----1111');
          return (
            <div className="lego-date-picker-filter">
              <DateRangePicker
                // @ts-ignore
                key={key}
                isMobile={isMobile()}
                onChange={onChange}
                presets={presetsFun}
                min={new Date('2021/01/01')}
                max={dayjs().toDate()}
                // 最多可选天数，根据配置动态设置
                maxStep={dateRange - 1}
                defaultValue={curDefaultValue}
                allowClear={false}
                placement="bottomLeft"
              />
            </div>
            // <DatePickerFilterView
            //   key={key}
            //   value={value}
            //   pickerType="picker-filter"
            //   handleChange={(value, dateFilterRelativeUnit) => {
            //     onChange(value);
            //     this.dateFilterRelativeUnit = dateFilterRelativeUnit;
            //   }}
            //   defaultValue={defaultDateItem}
            //   maxStep={dateRange - 1}
            // />
          );
        }}
      </FilterWrapper>
    );
  } else {
    return <div className="lego-filter-wrap">请选择日期测试</div>;
  }
};

DatePickerFilter.displayName = '时间筛选器';
