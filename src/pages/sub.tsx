import LegoRender from '@blm/bi-lego-sdk/LegoRender';
import { report } from '@blm/bi-lego-sdk/utils';
import React from 'react';
import dayjs from 'dayjs';

window.report = report;

const filterProps = {
  adcode: {
    // 默认值
    defaultValue: ['110100'],
    // clearable: true,
  },
  car_team_id: {
    defaultValue: [3805],
  },
  fleet_name: {
    defaultValue: ['YSL-默认车队'],
  },
  proname: {
    defaultValue: ['约约出行'],
  },
  cnt_ord_done: {
    defaultValue: [0, 100],
  },
  // t_date: {
  //   defaultValue: {
  //     category: 'custom',
  //     dateType: 'customDate',
  //     value: [
  //       new Date('2025-07-03').toString(),
  //       new Date('2025-07-11').toString(),
  //     ],
  //   },
  // },
  // 日筛选: {
  //   defaultValue: {
  //     category: 'custom',
  //     dateType: 'customDate',
  //     value: [
  //       new Date('2025-06-03').toString(),
  //       new Date('2025-06-11').toString(),
  //     ],
  //     // value: [
  //     //   dayjs('2015/01/01', 'YYYY/MM/DD'),
  //     //   dayjs('2015/02/01', 'YYYY/MM/DD'),
  //     // ],
  //   },
  // },
};
export default () => {
  const [show, setShow] = React.useState(false);
  return (
    <>
      <LegoRender
        filterProps={filterProps}
        reportId="1780498439921534871"
      ></LegoRender>
      {/* <LegoRender
        filterProps={filterProps}
        reportKey="operationalDataMonitoring"
        // reportId="1780498439921534211"
      ></LegoRender>
      <LegoRender
        // filterProps={filterProps}
        reportKey="financeBusinessAnalysis"
      ></LegoRender> */}
    </>
  );
};
